#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据匹配脚本
功能：将对比表_最终合并.xlsx中的综合单价匹配填充到统计表.xlsx中
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import sys

def match_and_fill_data():
    """
    主要功能函数：匹配两个Excel文件的D列数据，并填充综合单价
    """
    try:
        # 读取统计表.xlsx
        print("正在读取统计表.xlsx...")
        stats_file = "统计表.xlsx"
        stats_wb = load_workbook(stats_file)
        stats_ws = stats_wb.active  # 使用默认的活动工作表
        
        # 读取对比表_最终合并.xlsx的"合并数据"sheet页
        print("正在读取对比表_最终合并.xlsx...")
        compare_file = "对比表_最终合并.xlsx"
        compare_wb = load_workbook(compare_file)
        
        # 查找"合并数据"工作表
        if "合并数据" in compare_wb.sheetnames:
            compare_ws = compare_wb["合并数据"]
        else:
            print("可用的工作表名称：", compare_wb.sheetnames)
            # 如果没有找到"合并数据"，尝试使用第一个工作表
            compare_ws = compare_wb.active
            print(f"未找到'合并数据'工作表，使用工作表：{compare_ws.title}")
        
        # 读取统计表的D列（设备名称）
        print("读取统计表D列数据...")
        stats_devices = {}  # 存储 {行号: 设备名称}
        for row in range(2, stats_ws.max_row + 1):  # 从第2行开始（假设第1行是标题）
            device_name = stats_ws[f'D{row}'].value
            if device_name:
                stats_devices[row] = str(device_name).strip()
        
        print(f"统计表中找到 {len(stats_devices)} 个设备名称")
        
        # 读取对比表的D列（项目名称与特征）和O列（综合单价）
        print("读取对比表D列和O列数据...")
        compare_data = {}  # 存储 {项目名称: 综合单价}
        for row in range(2, compare_ws.max_row + 1):  # 从第2行开始
            project_name = compare_ws[f'D{row}'].value
            unit_price = compare_ws[f'O{row}'].value
            if project_name and unit_price is not None:
                compare_data[str(project_name).strip()] = unit_price
        
        print(f"对比表中找到 {len(compare_data)} 个项目数据")
        
        # 进行匹配和填充
        print("开始匹配数据...")
        matched_count = 0
        unmatched_devices = []
        
        for row_num, device_name in stats_devices.items():
            if device_name in compare_data:
                # 找到匹配，填充到I列
                stats_ws[f'I{row_num}'] = compare_data[device_name]
                matched_count += 1
                print(f"匹配成功：{device_name} -> {compare_data[device_name]}")
            else:
                unmatched_devices.append(device_name)
        
        # 保存修改后的统计表
        output_file = "统计表_已填充.xlsx"
        stats_wb.save(output_file)
        
        # 输出结果统计
        print(f"\n=== 匹配结果统计 ===")
        print(f"总设备数量：{len(stats_devices)}")
        print(f"成功匹配：{matched_count}")
        print(f"未匹配：{len(unmatched_devices)}")
        print(f"匹配率：{matched_count/len(stats_devices)*100:.1f}%")
        
        if unmatched_devices:
            print(f"\n未匹配的设备名称：")
            for device in unmatched_devices[:10]:  # 只显示前10个
                print(f"  - {device}")
            if len(unmatched_devices) > 10:
                print(f"  ... 还有 {len(unmatched_devices) - 10} 个未显示")
        
        print(f"\n结果已保存到：{output_file}")
        
        # 显示一些样本数据用于验证
        print(f"\n=== 样本数据预览 ===")
        print("统计表设备名称样本：")
        for i, (row, device) in enumerate(list(stats_devices.items())[:5]):
            print(f"  行{row}: {device}")
        
        print("\n对比表项目名称样本：")
        for i, (project, price) in enumerate(list(compare_data.items())[:5]):
            print(f"  {project}: {price}")
            
    except FileNotFoundError as e:
        print(f"文件未找到：{e}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    match_and_fill_data()
